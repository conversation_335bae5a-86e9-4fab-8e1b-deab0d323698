#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create and initialize a test database in Odoo.
This script handles the common issues with Odoo 18.0 database initialization.
"""

import os
import sys
import subprocess
import time
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def check_postgres_connection():
    """Check if PostgreSQL is accessible."""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port="5432",
            user="odoo",
            password="odoo",
            database="postgres"
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        print("✓ PostgreSQL connection successful")
        return conn
    except Exception as e:
        print(f"✗ PostgreSQL connection failed: {e}")
        return None

def drop_database_if_exists(conn, db_name):
    """Drop database if it exists."""
    try:
        cursor = conn.cursor()
        cursor.execute(f"DROP DATABASE IF EXISTS {db_name};")
        print(f"✓ Dropped database '{db_name}' if it existed")
        cursor.close()
        return True
    except Exception as e:
        print(f"✗ Failed to drop database: {e}")
        return False

def create_empty_database(conn, db_name):
    """Create an empty database."""
    try:
        cursor = conn.cursor()
        cursor.execute(f"CREATE DATABASE {db_name} OWNER odoo;")
        print(f"✓ Created empty database '{db_name}'")
        cursor.close()
        return True
    except Exception as e:
        print(f"✗ Failed to create database: {e}")
        return False

def initialize_odoo_database(db_name):
    """Initialize Odoo database with base modules."""
    try:
        # First, try to initialize with minimal modules
        cmd = [
            sys.executable, "odoo-bin",
            "--config", "odoo.conf",
            "--database", db_name,
            "--init", "base",
            "--without-demo", "all",
            "--stop-after-init",
            "--log-level", "error"
        ]
        
        print(f"Initializing database '{db_name}' with base modules...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✓ Database '{db_name}' initialized successfully")
            return True
        else:
            print(f"✗ Database initialization failed:")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ Database initialization timed out")
        return False
    except Exception as e:
        print(f"✗ Database initialization failed: {e}")
        return False

def verify_database(db_name):
    """Verify that the database is properly initialized."""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port="5432",
            user="odoo",
            password="odoo",
            database=db_name
        )
        cursor = conn.cursor()
        
        # Check if basic Odoo tables exist
        cursor.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name IN ('ir_model', 'res_users', 'ir_module_module')
        """)
        tables = cursor.fetchall()
        
        if len(tables) >= 3:
            print(f"✓ Database '{db_name}' has required Odoo tables")
            
            # Check if admin user exists
            cursor.execute("SELECT count(*) FROM res_users WHERE login = 'admin'")
            admin_count = cursor.fetchone()[0]
            
            if admin_count > 0:
                print(f"✓ Admin user exists in database '{db_name}'")
                cursor.close()
                conn.close()
                return True
            else:
                print(f"✗ Admin user not found in database '{db_name}'")
        else:
            print(f"✗ Database '{db_name}' missing required tables")
            
        cursor.close()
        conn.close()
        return False
        
    except Exception as e:
        print(f"✗ Database verification failed: {e}")
        return False

def main():
    """Main function to create and initialize test database."""
    db_name = "test"
    
    print("=== Odoo Test Database Creation ===")
    print(f"Creating database: {db_name}")
    
    # Step 1: Check PostgreSQL connection
    conn = check_postgres_connection()
    if not conn:
        return False
    
    # Step 2: Drop existing database if it exists
    if not drop_database_if_exists(conn, db_name):
        conn.close()
        return False
    
    # Step 3: Create empty database
    if not create_empty_database(conn, db_name):
        conn.close()
        return False
    
    conn.close()
    
    # Step 4: Initialize Odoo database
    if not initialize_odoo_database(db_name):
        return False
    
    # Step 5: Verify database
    if verify_database(db_name):
        print(f"\n✓ SUCCESS: Database '{db_name}' created and initialized successfully!")
        print(f"You can now access it at: http://localhost:8069")
        print(f"Default login: admin")
        print(f"Default password: admin")
        return True
    else:
        print(f"\n✗ FAILED: Database '{db_name}' creation failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
